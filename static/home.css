/* Home page specific styles */
body {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    min-height: 100vh;
}

.home-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
}

.welcome-section {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    text-align: center;
}

.welcome-section h1 {
    color: #2d3436;
    font-size: 32px;
    margin-bottom: 15px;
}

.welcome-section h2 {
    color: #0984e3;
    font-size: 24px;
    margin-bottom: 20px;
}

.welcome-section p {
    color: #636e72;
    font-size: 16px;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto 30px;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons a {
    background-color: #0984e3;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-buttons a:hover {
    background-color: #74b9ff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.action-buttons a:last-child {
    background-color: #e17055;
}

.action-buttons a:last-child:hover {
    background-color: #d63031;
}

.user-info {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.user-info h3 {
    color: #2d3436;
    margin-bottom: 20px;
    font-size: 20px;
    border-bottom: 2px solid #ddd;
    padding-bottom: 10px;
}

.user-info p {
    color: #636e72;
    margin-bottom: 15px;
    line-height: 1.6;
}

.user-info strong {
    color: #2d3436;
}

/* Responsive design */
@media (max-width: 600px) {
    .home-container {
        padding: 20px 15px;
    }
    
    .welcome-section,
    .user-info {
        padding: 25px 20px;
    }
    
    .welcome-section h1 {
        font-size: 26px;
    }
    
    .welcome-section h2 {
        font-size: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .action-buttons a {
        width: 100%;
        max-width: 250px;
        text-align: center;
    }
}
