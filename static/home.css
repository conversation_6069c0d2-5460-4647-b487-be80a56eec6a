/* Home page specific styles */
body {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    min-height: 100vh;
}

.home-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
}

.welcome-section {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    text-align: center;
}

.welcome-section h1 {
    color: #2d3436;
    font-size: 32px;
    margin-bottom: 15px;
}

.welcome-section h2 {
    color: #0984e3;
    font-size: 24px;
    margin-bottom: 20px;
}

.welcome-section p {
    color: #636e72;
    font-size: 16px;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto 30px;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons a {
    background-color: #0984e3;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-buttons a:hover {
    background-color: #74b9ff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.action-buttons button {
    background-color: #e17055;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 16px;
}

.action-buttons button:hover {
    background-color: #d63031;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.user-info {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.user-info h3 {
    color: #2d3436;
    margin-bottom: 20px;
    font-size: 20px;
    border-bottom: 2px solid #ddd;
    padding-bottom: 10px;
}

.user-info p {
    color: #636e72;
    margin-bottom: 15px;
    line-height: 1.6;
}

.user-info strong {
    color: #2d3436;
}

/* Responsive design */
@media (max-width: 600px) {
    .home-container {
        padding: 20px 15px;
    }

    .welcome-section,
    .user-info {
        padding: 25px 20px;
    }

    .welcome-section h1 {
        font-size: 26px;
    }

    .welcome-section h2 {
        font-size: 20px;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .action-buttons a,
    .action-buttons button {
        width: 100%;
        max-width: 250px;
        text-align: center;
    }
}

/* Dialog styles */
.dialog-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.dialog-content {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    text-align: center;
    max-width: 400px;
    margin: 20px;
    animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }

    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.dialog-content h3 {
    color: #2d3436;
    font-size: 24px;
    margin-bottom: 15px;
}

.dialog-content p {
    color: #636e72;
    font-size: 16px;
    margin-bottom: 25px;
    line-height: 1.6;
}

.dialog-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-danger {
    background-color: #e17055 !important;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.btn-danger:hover {
    background-color: #d63031 !important;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-cancel {
    background-color: #74b9ff;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.btn-cancel:hover {
    background-color: #0984e3;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Responsive dialog */
@media (max-width: 500px) {
    .dialog-content {
        margin: 20px 15px;
        padding: 25px 20px;
    }

    .dialog-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn-danger,
    .btn-cancel {
        width: 100%;
        max-width: 200px;
    }
}