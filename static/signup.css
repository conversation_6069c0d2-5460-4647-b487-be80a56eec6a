/* Signup page specific styles */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px 0;
}

.signup-container {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    max-width: 500px;
    margin: 0 auto;
}

.signup-container h1 {
    color: #333;
    margin-bottom: 10px;
    font-size: 28px;
}

.signup-container p {
    color: #666;
    margin-bottom: 30px;
    text-align: center;
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-row .form-group {
    flex: 1;
}

.password-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    font-size: 18px;
}

.login-link {
    text-align: center;
    margin-top: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.login-link a {
    font-weight: 500;
}

/* Form adjustments for signup */
form {
    margin-bottom: 0;
    box-shadow: none;
    padding: 0;
}

input[type="text"],
input[type="email"],
input[type="password"] {
    padding: 15px;
    font-size: 16px;
    border: 2px solid #e1e5e9;
}

button[type="submit"] {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
    margin-top: 20px;
}

small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 13px;
}

/* Responsive design */
@media (max-width: 600px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .signup-container {
        margin: 0 20px;
        padding: 30px 25px;
    }
}
