/* Logout confirmation page specific styles */
body {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
}

.logout-container {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    text-align: center;
    max-width: 400px;
    margin: 20px;
}

.logout-container h1 {
    color: #2d3436;
    font-size: 28px;
    margin-bottom: 20px;
}

.logout-container p {
    color: #636e72;
    font-size: 16px;
    margin-bottom: 30px;
    line-height: 1.6;
}

.logout-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.logout-actions button {
    background-color: #e17055;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.logout-actions button:hover {
    background-color: #d63031;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.logout-actions a {
    background-color: #74b9ff;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 120px;
    display: inline-block;
    text-align: center;
}

.logout-actions a:hover {
    background-color: #0984e3;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Form adjustments */
form {
    display: inline-block;
    margin: 0;
    padding: 0;
    background: none;
    box-shadow: none;
}

/* Responsive design */
@media (max-width: 500px) {
    .logout-container {
        margin: 20px 15px;
        padding: 30px 25px;
    }
    
    .logout-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .logout-actions button,
    .logout-actions a {
        width: 100%;
        max-width: 200px;
    }
}
