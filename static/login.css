/* Login page specific styles */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
}

.login-container {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
    margin: 20px;
}

.login-container h1 {
    color: #333;
    margin-bottom: 10px;
    font-size: 28px;
}

.login-container p {
    color: #666;
    margin-bottom: 30px;
    text-align: center;
}

.password-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    font-size: 18px;
}

.signup-link {
    text-align: center;
    margin-top: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.signup-link a {
    font-weight: 500;
}

/* Form adjustments for login */
form {
    margin-bottom: 0;
    box-shadow: none;
    padding: 0;
}

input[type="email"],
input[type="password"] {
    padding: 15px;
    font-size: 16px;
    border: 2px solid #e1e5e9;
}

button[type="submit"] {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
    margin-top: 10px;
}
