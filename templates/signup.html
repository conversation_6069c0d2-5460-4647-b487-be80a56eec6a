{% extends "base.html" %}

{% block title %}Inscription - Portail PDF Étudiant{% endblock %}

{% block content %}
<h1>Créer un compte</h1>
<p>Rejoignez EduShare pour commencer à partager vos documents</p>

<form method="POST" autocomplete="off">
    <div>
        <label for="first_name">Prénom *</label>
        <input type="text" id="first_name" name="first_name" placeholder="Votre prénom" required autocomplete="off" maxlength="50">
    </div>

    <div>
        <label for="last_name">Nom *</label>
        <input type="text" id="last_name" name="last_name" placeholder="Votre nom" required autocomplete="off" maxlength="50">
    </div>

    <div>
        <label for="email">Adresse email *</label>
        <input type="email" id="email" name="email" placeholder="<EMAIL>" required autocomplete="off" maxlength="100">
    </div>

    <div>
        <label for="password">Mot de passe *</label>
        <div style="position: relative;">
            <input type="password" id="password" name="password" placeholder="Créez un mot de passe sécurisé" required autocomplete="new-password" maxlength="200" minlength="8">
            <button type="button" onclick="togglePassword('password')" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer;">
                👁️
            </button>
        </div>
        <small>Le mot de passe doit contenir au moins 8 caractères.</small>
    </div>

    <div>
        <label for="confirm_password">Confirmer le mot de passe *</label>
        <div style="position: relative;">
            <input type="password" id="confirm_password" name="confirm_password" placeholder="Confirmez votre mot de passe" required autocomplete="new-password" maxlength="200" minlength="8">
            <button type="button" onclick="togglePassword('confirm_password')" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer;">
                👁️
            </button>
        </div>
    </div>

    <button type="submit">Créer mon compte</button>
</form>

<div>
    Vous avez déjà un compte? <a href="{{ url_for('login') }}">Connectez-vous</a>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;

    if (field.type === 'password') {
        field.type = 'text';
        button.textContent = '🙈';
    } else {
        field.type = 'password';
        button.textContent = '👁️';
    }
}
</script>
{% endblock %}
