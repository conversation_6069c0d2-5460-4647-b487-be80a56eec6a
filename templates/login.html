{% extends "base.html" %}

{% block title %}Connexion - Portail PDF Étudiant{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='login.css') }}">
{% endblock %}

{% block content %}
<div class="login-container">
    <h1>Bon retour</h1>
    <p>Connectez-vous à votre compte pour continuer</p>

    <form method="POST" autocomplete="on">
        <div class="form-group">
            <label for="email">Adresse email</label>
            <input type="email" id="email" name="email" placeholder="Entrez votre email" required autocomplete="username" maxlength="100">
        </div>

        <div class="form-group">
            <label for="password">Mot de passe</label>
            <div class="password-container">
                <input type="password" id="password" name="password" placeholder="Entrez votre mot de passe" required autocomplete="current-password" maxlength="200" minlength="8">
                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                    👁️
                </button>
            </div>
        </div>

        <button type="submit">Se connecter</button>
    </form>

    <div class="signup-link">
        Vous n'avez pas de compte? <a href="{{ url_for('signup') }}">Inscrivez-vous</a>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;

    if (field.type === 'password') {
        field.type = 'text';
        button.textContent = '🙈';
    } else {
        field.type = 'password';
        button.textContent = '👁️';
    }
}
</script>
{% endblock %}
